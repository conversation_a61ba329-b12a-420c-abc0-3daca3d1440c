<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.yanbao.mapper.ProductOrderInsuranceMapper">

    <resultMap id="voResultMap" type="com.ruoyi.yanbao.entity.vo.ProductOrderInsuranceVo">
        <!-- ProductOrderInsurance fields -->
        <result column="id" property="id"/>
        <result column="created_at" property="createdAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="changed_at" property="changedAt"/>
        <result column="changed_by" property="changedBy"/>
        <result column="product_order_id" property="productOrderId"/>
        <result column="insurance_state" property="insuranceState"/>
        <result column="insurance_no" property="insuranceNo"/>
        <result column="insurance_date" property="insuranceDate"/>
        <result column="begin_time" property="beginTime"/>
        <result column="end_time" property="endTime"/>
        <result column="price" property="price"/>
        <result column="insurance_file" property="insuranceFile"/>
        <result column="insurance_company_id" property="insuranceCompanyId"/>

        <!-- ProductOrderInsuranceVo additional fields from ProductOrder -->
        <result column="customer_name" property="customerName"/>
        <result column="vin_no" property="vinNo"/>
        <result column="car_no" property="carNo"/>
        <result column="contact_telephone" property="contactTelephone"/>
        <result column="sale_user_id" property="saleUserId"/>
        <result column="store_id" property="storeId"/>
        <result column="product_id" property="productId"/>
        <result column="order_state" property="orderState"/>

        <!-- Additional fields for display -->
        <result column="order_no" property="orderNo"/>
        <result column="product_name" property="productName"/>
        <result column="store_name" property="storeName"/>
        <result column="sale_user_name" property="saleUserName"/>
        <result column="insurance_company_name" property="insuranceCompanyName"/>
    </resultMap>

    <select id="queryList" parameterType="com.ruoyi.yanbao.entity.vo.ProductOrderInsuranceVo" resultMap="voResultMap">
        SELECT
        a.id,
        a.created_at,
        a.created_by,
        a.changed_at,
        a.changed_by,
        a.product_order_id,
        a.insurance_state,
        a.insurance_no,
        a.insurance_date,
        a.begin_time,
        a.end_time,
        a.price,
        a.insurance_file,
        a.insurance_company_id,
        b.order_state,
        b.customer_name,
        b.vin_no,
        b.car_no,
        b.contact_telephone,
        b.sale_user_id,
        b.store_id,
        b.product_id,
        b.order_no,
        b.product_name,
        b.store_name,
        b.sale_user_name,
        ic.name as insurance_company_name

        FROM p_product_order_insurance a
        LEFT JOIN p_product_order b ON a.product_order_id = b.id
        LEFT JOIN p_insurance_company ic ON a.insurance_company_id = ic.id
        WHERE a.is_delete = 0
        <if test="customerName != null and customerName != ''">
            AND b.customer_name LIKE CONCAT('%', #{customerName}, '%')
        </if>
        <if test="vinNo != null and vinNo != ''">
            AND b.vin_no LIKE CONCAT('%', #{vinNo}, '%')
        </if>
        <if test="carNo != null and carNo != ''">
            AND b.car_no LIKE CONCAT('%', #{carNo}, '%')
        </if>
        <if test="contactTelephone != null and contactTelephone != ''">
            AND b.contact_telephone LIKE CONCAT('%', #{contactTelephone}, '%')
        </if>
        <if test="insuranceState != null">
            AND a.insurance_state = #{insuranceState}
        </if>
        <if test="insuranceNo != null and insuranceNo != ''">
            AND a.insurance_no LIKE CONCAT('%', #{insuranceNo}, '%')
        </if>
        <if test="saleUserId != null">
            AND b.sale_user_id = #{saleUserId}
        </if>
        <if test="storeId != null">
            AND b.store_id = #{storeId}
        </if>
        <if test="productId != null">
            AND b.product_id = #{productId}
        </if>
        <if test="insuranceCompanyId != null">
            AND a.insurance_company_id = #{insuranceCompanyId}
        </if>
        ORDER BY a.created_at DESC
    </select>
</mapper>
